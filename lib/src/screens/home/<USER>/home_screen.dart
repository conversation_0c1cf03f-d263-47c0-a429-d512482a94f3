import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:opti_tickets/src/core/shared/extensions/context_extensions.dart';
import 'package:opti_tickets/src/core/theme/color_manager.dart';
import 'package:opti_tickets/src/screens/auth/models/user_model.dart';
import 'package:opti_tickets/src/screens/auth/providers/auth_providers.dart';
import 'package:opti_tickets/src/screens/reports_screen/view/reports_screen.dart';
import 'package:opti_tickets/src/screens/signatures/models/signature_model.dart';
import 'package:opti_tickets/src/screens/signatures/providers/signature_providers.dart';
import 'package:opti_tickets/src/screens/signatures/view/add_signature_sheet.dart';
import 'package:xr_helper/xr_helper.dart';

class HomeScreen extends HookConsumerWidget {
  const HomeScreen({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final selectedDate = useState(DateTime.now());
    final selectedUser = useState<UserModel?>(null);

    useEffect(() {
      WidgetsBinding.instance.addPostFrameCallback((_) {
        ref.read(signatureControllerProvider).initialize();
      });
      return () {};
    }, []);

    void showAddSignatureSheet() {
      showModalBottomSheet(
        context: context,
        isScrollControlled: true,
        backgroundColor: Colors.transparent,
        builder: (context) => const AddSignatureSheet(),
      ).then((result) {
        if (result == true) {
          // Refresh signatures after adding
          ref.read(signatureControllerProvider).getTodaySignatures();
        }
      });
    }

    void onDateSelected(DateTime date) {
      selectedDate.value = date;
      ref.read(signatureControllerProvider).setSelectedDate(date);
    }

    void onUserSelected(UserModel? user) {
      selectedUser.value = user;
      ref.read(signatureControllerProvider).setSelectedUser(user);
    }

    final currentUser = UserModel.currentUser();
    final signatureController = ref.watch(signatureControllerProvider);
    final todaySignatures = ref.watch(todaySignaturesFutureProvider);

    Widget _buildSignatureCard(SignatureModel signature) {
      return Container(
        margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(12),
          boxShadow: [
            BoxShadow(
              color: Colors.grey.withOpacity(0.1),
              spreadRadius: 1,
              blurRadius: 4,
              offset: const Offset(0, 2),
            ),
          ],
          border: signature.isFirstOfDay
              ? Border.all(color: Colors.green, width: 2)
              : null,
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Container(
                  padding:
                      const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                  decoration: BoxDecoration(
                    color: signature.isFirstOfDay
                        ? Colors.green.shade100
                        : Colors.blue.shade100,
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Text(
                    signature.isFirstOfDay
                        ? context.tr.firstOfDay
                        : context.tr.regularSignature,
                    style: TextStyle(
                      color: signature.isFirstOfDay
                          ? Colors.green.shade700
                          : Colors.blue.shade700,
                      fontSize: 12,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
                const Spacer(),
                Text(
                  signature.formattedTime,
                  style: context.textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 12),
            Row(
              children: [
                Icon(Icons.location_on,
                    color: ColorManager.primaryColor, size: 20),
                const SizedBox(width: 8),
                Expanded(
                  child: Text(
                    signature.place,
                    style: context.textTheme.titleMedium,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 8),
            Row(
              children: [
                Icon(Icons.person, color: Colors.grey[600], size: 20),
                const SizedBox(width: 8),
                Expanded(
                  child: Text(
                    signature.mandobName,
                    style: context.textTheme.bodyMedium?.copyWith(
                      color: Colors.grey[600],
                    ),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 8),
            Row(
              children: [
                Icon(Icons.gps_fixed, color: Colors.grey[600], size: 20),
                const SizedBox(width: 8),
                Expanded(
                  child: Text(
                    '${signature.lat.toStringAsFixed(6)}, ${signature.long.toStringAsFixed(6)}',
                    style: context.textTheme.bodySmall?.copyWith(
                      color: Colors.grey[600],
                    ),
                  ),
                ),
              ],
            ),
          ],
        ),
      );
    }

    return Scaffold(
      backgroundColor: ColorManager.lightGreyBackground,
      appBar: AppBar(
        title: Text(context.tr.signatures),
        backgroundColor: ColorManager.primaryColor,
        foregroundColor: Colors.white,
        actions: [
          if (currentUser.isAdmin)
            IconButton(
              icon: const Icon(Icons.assessment),
              onPressed: () {
                const ReportsScreen().navigate;
              },
            ),
          IconButton(
            icon: const Icon(Icons.logout),
            onPressed: () {
              ref.read(authControllerNotifierProvider).logout();
            },
          ),
        ],
      ),
      body: RefreshIndicator(
        onRefresh: () async {
          await ref.read(signatureControllerProvider).getTodaySignatures();
        },
        child: CustomScrollView(
          slivers: [
            // Date picker section
            SliverToBoxAdapter(
              child: Container(
                margin: const EdgeInsets.all(16),
                padding: const EdgeInsets.all(16),
                decoration: BoxDecoration(
                  color: Colors.white,
                  borderRadius: BorderRadius.circular(12),
                  boxShadow: [
                    BoxShadow(
                      color: Colors.grey.withOpacity(0.1),
                      spreadRadius: 1,
                      blurRadius: 4,
                      offset: const Offset(0, 2),
                    ),
                  ],
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      selectedDate.value.formatDateToString,
                      style: context.textTheme.headlineSmall?.copyWith(
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 8),
                    Row(
                      children: [
                        Icon(Icons.calendar_today,
                            color: ColorManager.primaryColor, size: 16),
                        const SizedBox(width: 8),
                        TextButton(
                          onPressed: () async {
                            final date = await showDatePicker(
                              context: context,
                              initialDate: selectedDate.value,
                              firstDate: DateTime.now()
                                  .subtract(const Duration(days: 365)),
                              lastDate: DateTime.now(),
                            );
                            if (date != null) {
                              onDateSelected(date);
                            }
                          },
                          child: Text(context.tr.selectDate),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
            ),

            // Admin user selector
            if (currentUser.isAdmin) ...[
              SliverToBoxAdapter(
                child: Container(
                  margin: const EdgeInsets.symmetric(horizontal: 16),
                  padding: const EdgeInsets.all(16),
                  decoration: BoxDecoration(
                    color: Colors.white,
                    borderRadius: BorderRadius.circular(12),
                    boxShadow: [
                      BoxShadow(
                        color: Colors.grey.withOpacity(0.1),
                        spreadRadius: 1,
                        blurRadius: 4,
                        offset: const Offset(0, 2),
                      ),
                    ],
                  ),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        context.tr.selectDelivery,
                        style: context.textTheme.titleMedium?.copyWith(
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      const SizedBox(height: 8),
                      DropdownButtonFormField<UserModel>(
                        value: selectedUser.value,
                        decoration: InputDecoration(
                          hintText: context.tr.allDeliveries,
                          border: OutlineInputBorder(
                            borderRadius: BorderRadius.circular(8),
                          ),
                        ),
                        items: [
                          DropdownMenuItem<UserModel>(
                            value: null,
                            child: Text(context.tr.allDeliveries),
                          ),
                          ...signatureController.users.map(
                            (user) => DropdownMenuItem<UserModel>(
                              value: user,
                              child: Text(user.name),
                            ),
                          ),
                        ],
                        onChanged: onUserSelected,
                      ),
                    ],
                  ),
                ),
              ),
              const SliverToBoxAdapter(child: SizedBox(height: 16)),
            ],

            // Signatures list
            todaySignatures.when(
              data: (signatures) => signatures.isEmpty
                  ? SliverToBoxAdapter(
                      child: Container(
                        margin: const EdgeInsets.all(16),
                        padding: const EdgeInsets.all(32),
                        decoration: BoxDecoration(
                          color: Colors.white,
                          borderRadius: BorderRadius.circular(12),
                        ),
                        child: Column(
                          children: [
                            Icon(
                              Icons.assignment_outlined,
                              size: 64,
                              color: Colors.grey[400],
                            ),
                            const SizedBox(height: 16),
                            Text(
                              context.tr.noSignaturesToday,
                              style: context.textTheme.titleMedium?.copyWith(
                                color: Colors.grey[600],
                              ),
                            ),
                          ],
                        ),
                      ),
                    )
                  : SliverList(
                      delegate: SliverChildBuilderDelegate(
                        (context, index) {
                          final signature = signatures[index];
                          return _buildSignatureCard(signature);
                        },
                        childCount: signatures.length,
                      ),
                    ),
              loading: () => const SliverToBoxAdapter(
                child: Center(child: CircularProgressIndicator()),
              ),
              error: (error, stack) => SliverToBoxAdapter(
                child: Container(
                  margin: const EdgeInsets.all(16),
                  padding: const EdgeInsets.all(16),
                  decoration: BoxDecoration(
                    color: Colors.red.shade50,
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Text(
                    'Error: $error',
                    style: TextStyle(color: Colors.red.shade700),
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
      floatingActionButton: !currentUser.isAdmin
          ? FloatingActionButton.extended(
              onPressed: showAddSignatureSheet,
              icon: const Icon(Icons.add),
              label: Text(context.tr.addSignature),
              backgroundColor: ColorManager.primaryColor,
            )
          : null,
    );
  }
}
