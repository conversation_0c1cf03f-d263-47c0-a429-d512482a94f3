import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_form_builder/flutter_form_builder.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:opti_tickets/src/core/consts/app_constants.dart';
import 'package:opti_tickets/src/core/consts/network/api_strings.dart';
import 'package:opti_tickets/src/core/shared/extensions/context_extensions.dart';
import 'package:opti_tickets/src/core/shared/widgets/fields/text_field.dart';
import 'package:opti_tickets/src/core/shared/widgets/loading/loading_widget.dart';
import 'package:opti_tickets/src/core/theme/color_manager.dart';
import 'package:opti_tickets/src/screens/auth/providers/auth_providers.dart';
import 'package:opti_tickets/src/screens/auth/view/register/register_screen.dart';
import 'package:xr_helper/xr_helper.dart';

class LoginFieldsContainer extends ConsumerWidget {
  final GlobalKey<FormBuilderState> formKey;

  const LoginFieldsContainer({super.key, required this.formKey});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final authController = ref.watch(authControllerNotifierProvider);

    void login() async {
      if (!formKey.currentState!.saveAndValidate()) return;
      // refreshData();
      final data = formKey.currentState?.instantValue ?? {};

      await authController.login(
        data: data,
      );

      // refreshData();
    }

    final isKeyboardOpened = MediaQuery.of(context).viewInsets.bottom > 0;

    return Container(
      padding: const EdgeInsets.all(AppSpaces.padding24),
      decoration: const BoxDecoration(
        color: ColorManager.white,
        borderRadius: BorderRadius.only(
          topRight: Radius.circular(AppRadius.radius50),
        ),
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // if (!isKeyboardOpened) AppGaps.gap48,

          Text(
            context.tr.itsGreatToSeeYou,
            style: AppTextStyles.subHeadLine,
          ),

          AppGaps.gap12,

          //! Email ------------------------------
          BaseTextField(
            initialValue: kDebugMode ? AppConsts.testEmail : '',
            name: FieldsConsts.username,
            title: context.tr.email,
            hint: context.tr.email,
            fillColor: ColorManager.grey,
            textInputType: TextInputType.emailAddress,
          ),

          AppGaps.gap12,

          //! Password ------------------------------
          BaseTextField(
            initialValue: kDebugMode ? AppConsts.testPass : '',
            name: FieldsConsts.password,
            title: context.tr.password,
            hint: '********',
            textInputType: TextInputType.visiblePassword,
            isObscure: true,
            withoutEnter: true,
            fillColor: ColorManager.grey,
          ),

          AppGaps.gap48,

          Button(
            isLoading: authController.isLoading,
            loadingWidget: const LoadingWidget(
              height: 60,
              width: 60,
            ),
            label: context.tr.login,
            onPressed: login,
          ),

          AppGaps.gap24,

          // Register link
          Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Expanded(child: Text(context.tr.dontHaveAccount)),
              TextButton(
                onPressed: () {
                  const RegisterScreen().navigate;
                },
                child: Text(context.tr.signUp),
              ),
            ],
          ),

          Padding(
            padding: EdgeInsets.only(
                bottom: MediaQuery.of(context).viewInsets.bottom),
          ),
        ],
      ),
    );
  }
}
