import 'package:equatable/equatable.dart';
import 'package:xr_helper/xr_helper.dart';

class UserModel extends Equatable {
  final String uid;
  final String name;
  final String email;
  final String? password;
  final bool isAdmin;

  const UserModel({
    this.uid = '',
    this.name = '',
    this.email = '',
    this.password,
    this.isAdmin = false,
  });

  // * From Firebase/Json ================================
  factory UserModel.fromJson(Map<String, dynamic> json) {
    return UserModel(
      uid: json['uid'] ?? '',
      name: json['name'] ?? '',
      email: json['email'] ?? '',
      password: json['password'],
      isAdmin: json['isAdmin'] ?? false,
    );
  }

  //? Copy With

  // * To Json ================================
  Map<String, dynamic> toJson() {
    return {
      'uid': uid,
      'name': name,
      'email': email,
      'isAdmin': isAdmin,
      if (password != null) 'password': password,
    };
  }

  // * To Firestore Json ================================
  Map<String, dynamic> toFirestoreJson() {
    return {
      'uid': uid,
      'name': name,
      'email': email,
      'password': password,
      'isAdmin': isAdmin,
      'createdAt': DateTime.now().formatDateToString,
    };
  }

  @override
  List<Object?> get props => [
        uid,
        name,
        email,
        password,
        isAdmin,
      ];

  // copy with method
  UserModel copyWith({
    String? uid,
    String? name,
    String? email,
    String? password,
    bool? isAdmin,
  }) {
    return UserModel(
      uid: uid ?? this.uid,
      name: name ?? this.name,
      email: email ?? this.email,
      password: password ?? this.password,
      isAdmin: isAdmin ?? this.isAdmin,
    );
  }

  //? Set current user
  static void setCurrentUser(UserModel user) {
    GetStorageService.setData(
      key: LocalKeys.user,
      value: user.toJson(),
    );
  }

  //? Get saved user
  static UserModel currentUser() {
    final userData = GetStorageService.getData(key: LocalKeys.user);

    if (userData == null) {
      return const UserModel();
    } else {
      return UserModel.fromJson(userData);
    }
  }
}
