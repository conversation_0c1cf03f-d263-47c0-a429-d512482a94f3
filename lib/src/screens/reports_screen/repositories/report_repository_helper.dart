import 'package:pdf/pdf.dart';
import 'package:pdf/src/widgets/font.dart';
import 'package:pdf/widgets.dart' as pw;

baseText(
  String text, {
  required Font arabicFont,
  bool isBold = false,
  bool isLtr = false,
  PdfColor? color,
}) {
  return pw.Padding(
    padding: const pw.EdgeInsets.all(2),
    child: pw.Text(
      text,
      textScaleFactor: 1,
      textAlign: pw.TextAlign.center,
      textDirection: pw.TextDirection.rtl,
      style: pw.TextStyle(
        font: arabicFont,
        fontWeight: isBold ? pw.FontWeight.bold : pw.FontWeight.normal,
        color: color,
      ),
    ),
  );
}
